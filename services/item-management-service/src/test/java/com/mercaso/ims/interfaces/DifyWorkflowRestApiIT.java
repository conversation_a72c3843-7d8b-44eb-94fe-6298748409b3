package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.DifyCategoryMatchingFinalResultDto;
import com.mercaso.ims.application.dto.DifyCategoryMatchingResultDto;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import java.io.IOException;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

class DifyWorkflowRestApiIT extends AbstractIT {

    @MockBean
    private com.mercaso.ims.infrastructure.external.dify.DifyApiClient difyApiClient;

    @Test
    void shouldSuccessWhenGetRecommendedCategories() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " smartphone product description";

        String mockResultJson = """
            {
                "finalResult": {
                    "matchedCategoryFullPath": "Electronics > Mobile Phones > Smartphones > Android Phones",
                    "matchedDepartment": "Electronics",
                    "matchedDepartmentId": "DEPT001",
                    "matchedCategory": "Mobile Phones",
                    "matchedCategoryId": "CAT001",
                    "matchedSubCategory": "Smartphones",
                    "matchedSubCategoryId": "SUB001",
                    "matchedClazz": "Android Phones",
                    "matchedClazzId": "CLS001",
                    "matchedCredibility": "0.95",
                    "recommendedCategory": "Electronics > Mobile Phones > Smartphones > Android Phones",
                    "recommendedCredibility": "0.95"
                },
                "analysis": "Based on the product description, this item best matches the Android Phones category.",
                "categoryOptimizationSuggestions": "Consider adding more specific brand information for better categorization."
            }
            """;

        DifyWorkflowResult mockWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId("test-workflow-123")
                .totalTokens(150L)
                .totalSteps(5)
                .elapsedTime(2.5)
                .result(mockResultJson)
                .status("succeeded")
                .build();

        when(difyApiClient.callDifyWorkflow(eq("description"), eq(description)))
                .thenReturn(mockWorkflowResult);

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFinalResult());
        assertNotNull(result.getAnalysis());
        assertNotNull(result.getCategoryOptimizationSuggestions());

        // Verify final result details
        DifyCategoryMatchingResultDto finalResult = result.getFinalResult();
        assertEquals("Electronics > Mobile Phones > Smartphones > Android Phones", finalResult.getMatchedCategoryFullPath());
        assertEquals("Electronics", finalResult.getMatchedDepartment());
        assertEquals("DEPT001", finalResult.getMatchedDepartmentId());
        assertEquals("Mobile Phones", finalResult.getMatchedCategory());
        assertEquals("CAT001", finalResult.getMatchedCategoryId());
        assertEquals("Smartphones", finalResult.getMatchedSubCategory());
        assertEquals("SUB001", finalResult.getMatchedSubCategoryId());
        assertEquals("Android Phones", finalResult.getMatchedClazz());
        assertEquals("CLS001", finalResult.getMatchedClazzId());
        assertEquals("0.95", finalResult.getMatchedCredibility());
        assertEquals("Electronics > Mobile Phones > Smartphones > Android Phones", finalResult.getRecommendedCategory());
        assertEquals("0.95", finalResult.getRecommendedCredibility());

        assertEquals("Based on the product description, this item best matches the Android Phones category.", result.getAnalysis());
        assertEquals("Consider adding more specific brand information for better categorization.", result.getCategoryOptimizationSuggestions());
    }