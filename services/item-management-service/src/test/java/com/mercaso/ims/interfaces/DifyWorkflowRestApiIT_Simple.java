package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.dto.DifyCategoryMatchingFinalResultDto;
import com.mercaso.ims.application.dto.DifyCategoryMatchingResultDto;
import com.mercaso.ims.domain.category.Category;
import com.mercaso.ims.domain.difyworkflowsrecord.DifyWorkflowRecord;
import com.mercaso.ims.infrastructure.external.dify.dto.DifyWorkflowResult;
import java.io.IOException;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

class DifyWorkflowRestApiIT_Simple extends AbstractIT {

    @MockBean
    private com.mercaso.ims.infrastructure.external.dify.DifyApiClient difyApiClient;

    @MockBean
    private com.mercaso.ims.domain.category.service.CategoryService categoryService;

    @MockBean
    private com.mercaso.ims.domain.difyworkflowsrecord.service.DifyWorkflowRecordService difyWorkflowRecordService;

    @Test
    void shouldSuccessWhenGetRecommendedCategories() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " smartphone product description";
        UUID validCategoryId = UUID.randomUUID();

        String mockResultJson = """
            {
                "finalResult": {
                    "matchedCategoryFullPath": "Electronics > Mobile Phones",
                    "matchedDepartment": "Electronics",
                    "matchedClazzId": "%s",
                    "matchedCredibility": "0.95"
                },
                "analysis": "Based on the product description, this item best matches the Mobile Phones category."
            }
            """.formatted(validCategoryId);

        DifyWorkflowResult mockWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId("test-workflow-123")
                .totalTokens(150L)
                .totalSteps(5)
                .elapsedTime(2.5)
                .result(mockResultJson)
                .status("succeeded")
                .build();

        // Mock category service to return a valid category
        Category mockCategory = Category.builder()
                .id(validCategoryId)
                .name("Mobile Phones")
                .build();

        when(difyApiClient.callDifyWorkflow(eq("description"), eq(description)))
                .thenReturn(mockWorkflowResult);
        when(categoryService.findById(validCategoryId))
                .thenReturn(mockCategory);
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(DifyWorkflowRecord.builder().id(UUID.randomUUID()).build());

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFinalResult());
        assertNotNull(result.getAnalysis());
        
        // Verify final result details
        DifyCategoryMatchingResultDto finalResult = result.getFinalResult();
        assertEquals("Electronics > Mobile Phones", finalResult.getMatchedCategoryFullPath());
        assertEquals("Electronics", finalResult.getMatchedDepartment());
        assertEquals(validCategoryId.toString(), finalResult.getMatchedClazzId());
        assertEquals("0.95", finalResult.getMatchedCredibility());
        
        assertEquals("Based on the product description, this item best matches the Mobile Phones category.", result.getAnalysis());
    }

    @Test
    void shouldReturnEmptyResultWhenDescriptionIsBlank() throws Exception {
        // Given
        String description = "";

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNull(result.getFinalResult());
        assertNull(result.getAnalysis());
        assertNull(result.getCategoryOptimizationSuggestions());
    }

    @Test
    void shouldReturnEmptyResultWhenDifyApiClientThrowsException() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " test product";
        
        when(difyApiClient.callDifyWorkflow(eq("description"), eq(description)))
                .thenThrow(new IOException("Dify API connection failed"));

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNull(result.getFinalResult());
        assertNull(result.getAnalysis());
        assertNull(result.getCategoryOptimizationSuggestions());
    }

    @Test
    void shouldReturnEmptyResultWhenCategoryNotFound() throws Exception {
        // Given
        String description = RandomStringUtils.randomAlphabetic(10) + " test product";
        UUID invalidCategoryId = UUID.randomUUID();

        String mockResultJson = """
            {
                "finalResult": {
                    "matchedCategoryFullPath": "Unknown > Category",
                    "matchedDepartment": "Unknown",
                    "matchedClazzId": "%s",
                    "matchedCredibility": "0.50"
                },
                "analysis": "Category not found in database."
            }
            """.formatted(invalidCategoryId);

        DifyWorkflowResult mockWorkflowResult = DifyWorkflowResult.builder()
                .workflowRunId("test-workflow-456")
                .totalTokens(50L)
                .totalSteps(3)
                .elapsedTime(1.2)
                .result(mockResultJson)
                .status("succeeded")
                .build();

        when(difyApiClient.callDifyWorkflow(eq("description"), eq(description)))
                .thenReturn(mockWorkflowResult);
        when(categoryService.findById(invalidCategoryId))
                .thenReturn(null); // Category not found
        when(difyWorkflowRecordService.save(any(DifyWorkflowRecord.class)))
                .thenReturn(DifyWorkflowRecord.builder().id(UUID.randomUUID()).build());

        // When
        DifyCategoryMatchingFinalResultDto result = difyWorkflowRestApiUtil.getRecommendedCategories(description);

        // Then
        assertNotNull(result);
        assertNull(result.getFinalResult()); // Should return empty result when category not found
        assertNull(result.getAnalysis());
        assertNull(result.getCategoryOptimizationSuggestions());
    }
}
