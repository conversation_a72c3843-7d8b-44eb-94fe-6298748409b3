package com.mercaso.ims.application.service.impl;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

import com.mercaso.ims.application.command.CreateItemVersionCommand;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemVersionDto;
import com.mercaso.ims.application.mapper.itemversion.ItemVersionDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.service.ItemVersionApplicationService;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemversion.ItemVersion;
import com.mercaso.ims.domain.itemversion.ItemVersionFactory;
import com.mercaso.ims.domain.itemversion.service.ItemVersionService;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import jakarta.persistence.EntityManager;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class ItemVersionApplicationServiceImpl implements ItemVersionApplicationService {

    private final ItemVersionService itemVersionService;

    private final ItemVersionDtoApplicationMapper itemVersionDtoApplicationMapper;

    private final ItemQueryApplicationService itemQueryApplicationService;

    private final EntityManager entityManager;

    @Override
    public ItemVersionDto findLastVersion(UUID itemId) {
        List<ItemVersion> itemVersions = itemVersionService.findByItemId(itemId);

        if (itemVersions == null || itemVersions.isEmpty()) {
            return null;
        }

        // Find the version number with the largest number
        ItemVersion latestVersion = itemVersions.stream()
            .max(Comparator.comparingInt(ItemVersion::getVersionNumber))
            .orElse(null);

        return itemVersionDtoApplicationMapper.domainToDto(latestVersion);
    }

    @Override
    @Transactional(propagation = REQUIRES_NEW)
    @Retryable(
        retryFor = {ImsBusinessException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 500)
    )
    public ItemVersionDto save(CreateItemVersionCommand command) {

        log.info("Saving item version, itemId: {}", command.getItemId());

        try {
            ItemDto itemDto = itemQueryApplicationService.findById(command.getItemId());

            ItemVersionDto lastOldVersion = findLastVersion(command.getItemId());

            int versionNumber = null == lastOldVersion ? 0 : lastOldVersion.getVersionNumber();
            Integer lastVersionNumber = ++versionNumber;
            itemDto.setLastVersionNumber(lastVersionNumber);

            command.setItemDto(itemDto);
            command.setVersionNumber(lastVersionNumber);
            command.setSkuNumber(itemDto.getSkuNumber());

            ItemVersion itemVersion = ItemVersionFactory.createItemVersion(command);
            log.info("Saving itemVersion, versionNumber: {}", itemVersion.getVersionNumber());

            ItemVersion savedItemVersion = itemVersionService.save(itemVersion);
            entityManager.flush();
            return itemVersionDtoApplicationMapper.domainToDto(savedItemVersion);

        } catch (Exception e) {
            log.warn("Constraint violation occurred, will retry. ItemId: {}", command.getItemId());
            throw new ImsBusinessException(ErrorCodeEnums.ITEM_VERSION_DUPLICATE_KEY);
        }
    }
}
