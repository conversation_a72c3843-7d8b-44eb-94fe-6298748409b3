package com.mercaso.ims.infrastructure.excel.strategy;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.dto.ItemListDto;
import com.mercaso.ims.application.dto.ItemSerachDto;
import com.mercaso.ims.application.query.ItemQuery;
import com.mercaso.ims.application.queryservice.ItemQueryApplicationService;
import com.mercaso.ims.application.searchservice.ItemSearchApplicationService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.IntStream;
import java.util.stream.StreamSupport;

import static com.mercaso.ims.application.query.ItemQuery.getCustomFilterValue;

@Slf4j
public abstract class AbstractBulkExportStrategy implements BulkExportStrategy {

    private final ItemQueryApplicationService itemQueryApplicationService;
    private final Executor taskExecutor;
    private final ItemSearchApplicationService itemSearchApplicationService;

    protected AbstractBulkExportStrategy(
            ItemQueryApplicationService itemQueryApplicationService,
            Executor taskExecutor,
            ItemSearchApplicationService itemSearchApplicationService) {
        this.itemQueryApplicationService = itemQueryApplicationService;
        this.taskExecutor = taskExecutor;
        this.itemSearchApplicationService = itemSearchApplicationService;
    }

    protected List<ItemSerachDto> fetchItemsWithSimpleData(String customFilter) {
        return fetchItemsWithPagination(customFilter, this::processPageForSimpleData);
    }

    protected List<ItemSerachDto> fetchFullItemData(String customFilter) {
        return fetchItemsWithPagination(customFilter, this::processPageForFullData);
    }

    private List<ItemSerachDto> fetchItemsWithPagination(String customFilter, 
                                                        PageProcessor processor) {
        int pageSize = 2000;
        AtomicInteger pageNumber = new AtomicInteger(1);
        AtomicBoolean isLastPage = new AtomicBoolean(false);

        return StreamSupport.stream(
                Spliterators.spliteratorUnknownSize(new Iterator<List<ItemSerachDto>>() {
                    @Override
                    public boolean hasNext() {
                        return !isLastPage.get();
                    }

                    @Override
                    public List<ItemSerachDto> next() {
                        if (!hasNext()) {
                            throw new NoSuchElementException();
                        }

                        Map<String, String> customFilterValue = getCustomFilterValue(customFilter);
                        ItemQuery itemQuery = ItemQuery.builder()
                                .page(pageNumber.get())
                                .pageSize(pageSize)
                                .customFilter(customFilterValue)
                                .build();

                        PageResult result = processor.process(itemQuery, pageSize);
                        
                        if (result.items.size() < pageSize) {
                            isLastPage.set(true);
                        }
                        pageNumber.incrementAndGet();
                        return result.items;
                    }
                }, Spliterator.ORDERED), true)
                .flatMap(List::stream)
                .toList();
    }

    private PageResult processPageForSimpleData(ItemQuery itemQuery, int pageSize) {
        List<UUID> itemIds = itemSearchApplicationService.searchItemListIds(itemQuery);
        if (itemIds.isEmpty()) {
            return new PageResult(List.of());
        }

        int subPageSize = 200;
        int totalSize = itemIds.size();

        int batches = (int) Math.ceil((double) totalSize / subPageSize);

        List<CompletableFuture<List<ItemDto>>> futures = IntStream.range(0, batches)
                .mapToObj(i -> {
                    int start = i * subPageSize;
                    int end = Math.min(start + subPageSize, totalSize);
                    List<UUID> subPageItemIds = itemIds.subList(start, end);

                    return CompletableFuture.supplyAsync(() ->
                            itemQueryApplicationService.findByIdIn(subPageItemIds), taskExecutor);

                }).toList();

        List<ItemDto> allItemDtos = futures.stream()
                .map(future -> {
                    try {
                        return future.join();
                    } catch (Exception e) {
                        log.error("Error retrieving batch in getItemsByPage", e);
                        return Collections.<ItemDto>emptyList();
                    }
                })
                .flatMap(List::stream)
                .toList();

        List<ItemSerachDto> itemSerachDtos = allItemDtos.stream()
                .map(item -> {
                    ItemSerachDto supplierItem = new ItemSerachDto();
                    supplierItem.setId(item.getId());
                    supplierItem.setSkuNumber(item.getSkuNumber());
                    return supplierItem;
                })
                .toList();

        return new PageResult(itemSerachDtos);
    }

    private PageResult processPageForFullData(ItemQuery itemQuery, int pageSize) {
        ItemListDto itemListDto = itemSearchApplicationService.searchItemListV2(itemQuery);
        return new PageResult(itemListDto.getData());
    }

    @FunctionalInterface
    private interface PageProcessor {
        PageResult process(ItemQuery itemQuery, int pageSize);
    }

    private static class PageResult {
        final List<ItemSerachDto> items;
        
        PageResult(List<ItemSerachDto> items) {
            this.items = items;
        }
    }
}
