package com.mercaso.ims.domain.businessevent.enums;

import com.mercaso.ims.application.dto.BaseDto;
import com.mercaso.ims.application.dto.event.BulkExportRecordsApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryDeletedApplicationEvent;
import com.mercaso.ims.application.dto.event.CategoryUpdatedApplicationEvent;
import com.mercaso.ims.application.dto.event.CreateAnalyzeExpenseRecordApplicationEvent;
import com.mercaso.ims.application.dto.event.ExceptionRecordConfirmedApplicationEvent;
import com.mercaso.ims.application.dto.event.ExceptionRecordCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ExceptionRecordDisputedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestCompletedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestDetailCompletedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestDetailCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestDetailImsUpdatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestDetailShopifySynchronizedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestFailureProcessedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAdjustmentRequestProcessedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemBoundToPriceGroupApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemCostChangeRequestCreatedEvent;
import com.mercaso.ims.application.dto.event.ItemCostChangeRequestUpdatedEvent;
import com.mercaso.ims.application.dto.event.ItemCostCollectionCreatedEvent;
import com.mercaso.ims.application.dto.event.ItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemDeletedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemPriceGroupAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemPriceGroupCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.ItemUnboundFromPriceGroupApplicationEvent;
import com.mercaso.ims.application.dto.event.TestOrderCreatedApplicationEvent;
import com.mercaso.ims.application.dto.event.VendorItemAmendApplicationEvent;
import com.mercaso.ims.application.dto.event.VendorItemCreatedApplicationEvent;
import com.mercaso.ims.application.dto.payload.BulkExportRecordsPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryDeletedPayloadDto;
import com.mercaso.ims.application.dto.payload.CategoryUpdatedPayloadDto;
import com.mercaso.ims.application.dto.payload.CreateVendorPoAnalyzeExpenseRecordPayloadDto;
import com.mercaso.ims.application.dto.payload.ExceptionRecordConfirmedPayloadDto;
import com.mercaso.ims.application.dto.payload.ExceptionRecordCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ExceptionRecordDisputedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentFailureProcessedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCompletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailCompletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailImsUpdatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestProcessedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemBoundToPriceGroupPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCostChangeRequestCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCostChangeRequestUpdatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCostCollectionCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemDeletedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemPriceGroupAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemPriceGroupCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.ItemUnboundFromPriceGroupPayloadDto;
import com.mercaso.ims.application.dto.payload.TestOrderCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorItemCreatedPayloadDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnums {
    TEST_ORDER_CREATED(TestOrderCreatedPayloadDto.class, TestOrderCreatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_CREATED(ItemAdjustmentRequestCreatedPayloadDto.class,
        ItemAdjustmentRequestCreatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_CREATED(ItemAdjustmentRequestDetailCreatedPayloadDto.class,
        ItemAdjustmentRequestDetailCreatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_COMPLETED(ItemAdjustmentRequestDetailCompletedPayloadDto.class,
        ItemAdjustmentRequestDetailCompletedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_IMS_UPDATED(ItemAdjustmentRequestDetailImsUpdatedPayloadDto.class,
        ItemAdjustmentRequestDetailImsUpdatedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_DETAIL_SHOPIFY_SYNCHRONIZED(ItemAdjustmentRequestDetailShopifySynchronizedPayloadDto.class,
        ItemAdjustmentRequestDetailShopifySynchronizedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_COMPLETED(ItemAdjustmentRequestCompletedPayloadDto.class,
        ItemAdjustmentRequestCompletedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_PROCESSED(ItemAdjustmentRequestProcessedPayloadDto.class,
        ItemAdjustmentRequestProcessedApplicationEvent.class),
    ITEM_ADJUSTMENT_REQUEST_FAILURE_PROCESSED(ItemAdjustmentFailureProcessedPayloadDto.class,
        ItemAdjustmentRequestFailureProcessedApplicationEvent.class),

    ITEM_CREATED(ItemCreatedPayloadDto.class, ItemCreatedApplicationEvent.class),
    ITEM_AMEND(ItemAmendPayloadDto.class, ItemAmendApplicationEvent.class),
    ITEM_BOUND_TO_PRICE_GROUP(ItemBoundToPriceGroupPayloadDto.class, ItemBoundToPriceGroupApplicationEvent.class),
    ITEM_UNBOUND_FROM_PRICE_GROUP(ItemUnboundFromPriceGroupPayloadDto.class, ItemUnboundFromPriceGroupApplicationEvent.class),
    ITEM_DELETED(ItemDeletedPayloadDto.class, ItemDeletedApplicationEvent.class),
    ITEM_COST_COLLECTION_CREATED(ItemCostCollectionCreatedPayloadDto.class, ItemCostCollectionCreatedEvent.class),
    ITEM_COST_CHANGE_REQUEST_CREATED(ItemCostChangeRequestCreatedPayloadDto.class, ItemCostChangeRequestCreatedEvent.class),
    ITEM_COST_CHANGE_REQUEST_UPDATED(ItemCostChangeRequestUpdatedPayloadDto.class, ItemCostChangeRequestUpdatedEvent.class),
    ANALYZE_VENDOR_PO_RECORD_APPROVED(CreateVendorPoAnalyzeExpenseRecordPayloadDto.class,
        CreateAnalyzeExpenseRecordApplicationEvent.class),
    VENDOR_ITEM_AMEND(VendorItemAmendPayloadDto.class, VendorItemAmendApplicationEvent.class),
    VENDOR_ITEM_CREATED(VendorItemCreatedPayloadDto.class, VendorItemCreatedApplicationEvent.class),
    ITEM_PRICE_GROUP_CREATED(ItemPriceGroupCreatedPayloadDto.class, ItemPriceGroupCreatedApplicationEvent.class),
    ITEM_PRICE_GROUP_AMEND(ItemPriceGroupAmendPayloadDto.class, ItemPriceGroupAmendApplicationEvent.class),
    CATEGORY_CREATED(CategoryCreatedPayloadDto.class, CategoryCreatedApplicationEvent.class),
    CATEGORY_UPDATED(CategoryUpdatedPayloadDto.class, CategoryUpdatedApplicationEvent.class),
    CATEGORY_DELETED(CategoryDeletedPayloadDto.class, CategoryDeletedApplicationEvent.class),
    EXCEPTION_RECORD_CREATED(ExceptionRecordCreatedPayloadDto.class, ExceptionRecordCreatedApplicationEvent.class),
    EXCEPTION_RECORD_CONFIRMED(ExceptionRecordConfirmedPayloadDto.class, ExceptionRecordConfirmedApplicationEvent.class),
    EXCEPTION_RECORD_DISPUTED(ExceptionRecordDisputedPayloadDto.class, ExceptionRecordDisputedApplicationEvent.class),
    BULK_EXPORT_RECORDS(BulkExportRecordsPayloadDto.class, BulkExportRecordsApplicationEvent.class)

    ;

    private final Class<? extends BusinessEventPayloadDto<? extends BaseDto>> payloadClass;

    private final Class<? extends BaseApplicationEvent<? extends BusinessEventPayloadDto<? extends BaseDto>>> eventClass;

    public static EventTypeEnums forPayload(Class<?> payloadClass) {
        for (EventTypeEnums eventType : values()) {
            if (eventType.getPayloadClass() == payloadClass) {
                return eventType;
            }
        }
        return null;
    }

}
