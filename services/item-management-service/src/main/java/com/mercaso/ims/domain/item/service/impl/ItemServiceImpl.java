package com.mercaso.ims.domain.item.service.impl;

import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.ItemRepository;
import com.mercaso.ims.domain.item.enums.ItemUpcType;
import com.mercaso.ims.domain.item.service.ItemService;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ItemServiceImpl implements ItemService {

    private final ItemRepository itemRepository;

    @Override
    public Item findById(UUID id) {
        return itemRepository.findById(id);
    }

    @Override
    public Item findBySku(String sku) {
        return itemRepository.findBySku(sku);
    }

    @Override
    public Item save(Item item) {
        return itemRepository.save(item);
    }

    @Override
    public Item update(Item item) {
        return itemRepository.update(item);
    }

    @Override
    public Item delete(UUID id) {
        return itemRepository.deleteById(id);
    }

    @Override
    public Page<Item> findByCreatedAtBetween(Instant begin, Instant end, String photo, PageRequest pageRequest) {
        return itemRepository.findByCreatedAtBetween(begin, end, photo, pageRequest);
    }

    @Override
    public List<Item> findAllByUpcAndUpcType(String upc, ItemUpcType upcType) {
        return itemRepository.findAllByUpcAndUpcType(upc, upcType);
    }


}
