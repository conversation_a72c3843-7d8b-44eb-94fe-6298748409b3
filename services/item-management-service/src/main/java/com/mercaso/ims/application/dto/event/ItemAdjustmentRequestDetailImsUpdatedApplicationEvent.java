package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.ItemAdjustmentRequestDetailImsUpdatedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ItemAdjustmentRequestDetailImsUpdatedApplicationEvent extends BaseApplicationEvent<ItemAdjustmentRequestDetailImsUpdatedPayloadDto> {

    public ItemAdjustmentRequestDetailImsUpdatedApplicationEvent(Object source, ItemAdjustmentRequestDetailImsUpdatedPayloadDto payload) {
        super(source, payload);
    }
}

