package com.mercaso.ims.interfaces.rest.query;

import com.mercaso.ims.application.dto.BrandDto;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.application.queryservice.BrandQueryApplicationService;
import com.mercaso.ims.application.queryservice.VendorQueryApplicationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/v1/query/vendor", produces = {MediaType.APPLICATION_JSON_VALUE})
@RequiredArgsConstructor
public class QueryVendorRestApi {

    private final VendorQueryApplicationService vendorQueryApplicationService;

    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:vendors')")
    public List<VendorDto> searchVendors (@RequestParam(value = "vendorName", required = false) String vendorName) {
        log.info("[searchVendors] param name: {}.", vendorName);
        return vendorQueryApplicationService.queryOrFilterVendors(vendorName);
    }
}
