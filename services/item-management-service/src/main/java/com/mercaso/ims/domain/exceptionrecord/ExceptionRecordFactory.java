package com.mercaso.ims.domain.exceptionrecord;

import com.mercaso.ims.application.command.CreateExceptionRecordCommand;
import com.mercaso.ims.domain.exceptionrecord.enums.ExceptionRecordStatus;

public class ExceptionRecordFactory {

    public static ExceptionRecord create(CreateExceptionRecordCommand command) {
        return ExceptionRecord.builder()
            .businessEventId(command.getBusinessEventId())
            .entityId(command.getEntityId())
            .entityType(command.getEntityType())
            .status(ExceptionRecordStatus.PENDING_REVIEW)
            .exceptionType(command.getExceptionType())
            .description(command.getDescription())
            .build();
    }

    private ExceptionRecordFactory() {
    }
}
