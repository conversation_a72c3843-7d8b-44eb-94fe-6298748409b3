package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.CreateCategoryCommand;
import com.mercaso.ims.application.command.UpdateCategoryCommand;
import com.mercaso.ims.application.dto.CategoryDto;
import com.mercaso.ims.application.dto.CategoryTreeDto;
import com.mercaso.ims.application.service.CategoryApplicationService;
import java.io.IOException;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/v2/categories", produces = {MediaType.APPLICATION_JSON_VALUE})
@Slf4j
@RequiredArgsConstructor
public class CategoryV2RestApi {

    private final CategoryApplicationService categoryApplicationService;

    @GetMapping
    @PreAuthorize("hasAuthority('ims:read:categories')")
    public List<CategoryTreeDto> searchCategoryTree() {
        log.info("[searchCategories] Searching for category tree");
        return categoryApplicationService.searchCategoryTree();
    }

    @PostMapping
    @PreAuthorize("hasAuthority('ims:write:categories')")
    public CategoryDto createCategory(@RequestBody CreateCategoryCommand command) {
        log.info("[createCategory] param command: {}.", command);
        return categoryApplicationService.createCategory(command);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:categories')")
    public void updateCategory(@PathVariable("id") UUID id, @RequestBody UpdateCategoryCommand command) {
        log.info("[updateCategory] param, id:{}, command: {}.", id, command);
        categoryApplicationService.updateCategory(id, command);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ims:write:categories')")
    public void deleteCategory(@PathVariable("id") UUID id) {
        log.info("[deleteCategory] param, id:{}.", id);
        categoryApplicationService.deleteCategory(id);
    }

}