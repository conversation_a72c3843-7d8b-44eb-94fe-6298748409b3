package com.mercaso.ims.application.mapper.itemcostcollection;

import com.mercaso.ims.application.dto.ItemCostChangeRequestDto;
import com.mercaso.ims.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ItemCostChangeRequestApplicationMapper extends
    BaseDtoApplicationMapper<ItemCostChangeRequest, ItemCostChangeRequestDto> {

    @Mapping(target = "skuNumber", ignore = true)
    @Mapping(target = "regPrice", ignore = true)
    @Mapping(target = "primaryVendorId", ignore = true)
    @Mapping(target = "itemStatus", ignore = true)
    @Mapping(target = "isPrimaryVendor", ignore = true)
    @Mapping(target = "isBackupVendor", ignore = true)
    @Mapping(target = "backupVendorId", ignore = true)
    ItemCostChangeRequestDto domainToDto(ItemCostChangeRequest domain);

}
