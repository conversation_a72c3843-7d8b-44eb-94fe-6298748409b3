package com.mercaso.ims.application.dto;

import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemPackingInfoDto extends BaseDto {

    private UUID id;

    private String title;

    private String skuNumber;

    private String bottleSize;

    private String backupVendorName;

    private Integer packageSize;

    private String photo;

    private String photoUrl;

    private List<ItemUPCDto> itemUPCs;

    private List<VendorItemDto> vendorItemDtos;


}
