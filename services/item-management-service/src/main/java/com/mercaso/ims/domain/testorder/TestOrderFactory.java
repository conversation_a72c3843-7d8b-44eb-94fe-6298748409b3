package com.mercaso.ims.domain.testorder;

import com.mercaso.ims.application.command.CreateTestOrderCommand;

import java.time.Instant;

public class TestOrderFactory {

    public static TestOrder create(CreateTestOrderCommand command) {
        return TestOrder.builder()
                .name(command.getName())
                .status(command.getStatus())
                .createdAt(Instant.now())
                .createdBy("createUser")
                .build();
    }

    private TestOrderFactory() {
    }
}
