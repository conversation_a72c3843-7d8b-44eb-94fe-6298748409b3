package com.mercaso.ims.domain.brand;

import com.mercaso.ims.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface BrandRepository extends BaseDomainRepository<Brand, UUID> {

    Brand findByName(String name);

    List<Brand> findByIds(List<UUID> ids);

    List<Brand> findAll();

    List<Brand> findByFuzzyName (String name);

    List<Brand> findByNameIgnoreCase (String name);
}
