package com.mercaso.ims.infrastructure.process.parser;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.infrastructure.excel.data.CostcoItemDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.processor.CostcoItemDailyUpdateSheetProcessor;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class CostcoItemCostCollectionParser implements ItemCostCollectionParser {

    static final String COSTCO = VendorConstant.COSTCO;

    private final ItemCostCollectionService itemCostCollectionService;
    private final CostcoItemDailyUpdateSheetProcessor costcoItemDailyUpdateSheetProcessor;


    @Override
    public List<ItemCostCollectionItemParsingResultDto> parse(UUID itemCostCollectionId) {
        ItemCostCollection itemCostCollection = itemCostCollectionService.findById(itemCostCollectionId);

        if (null == itemCostCollection) {
            return new ArrayList<>();
        }
        List<CostcoItemDailyUpdatedData> costcoItemDailyUpdatedData = costcoItemDailyUpdateSheetProcessor.process(
            itemCostCollection.getFileName());
        return costcoItemDailyUpdatedData.stream().map(this::convertToItemCostCollectionItemParsingResultDto).toList();
    }


    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return COSTCO.equals(vendorName) && !sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    @Override
    public boolean isUpdateAvailability() {
        return true;
    }

    private ItemCostCollectionItemParsingResultDto convertToItemCostCollectionItemParsingResultDto(CostcoItemDailyUpdatedData data) {
        Boolean availability = true;
        if (data.getStatusDesc().equals("Deleted") || data.getStatusDesc().equals("Replaced") || data.getOnHand() < 9) {
            availability = false;
        }
        return ItemCostCollectionItemParsingResultDto.builder()
            .upc(data.getUpc())
            .cost(data.getFloorPrice())
            .vendorItemName(data.getDescription1())
            .vendorSkuNumber(data.getVendorItemNumber())
            .availability(availability)
            .build();
    }


}