package com.mercaso.ims.domain.attribute;

import com.mercaso.ims.application.command.CreateAttributeCommand;
import com.mercaso.ims.domain.attribute.enums.AttributeStatus;

public class AttributeFactory {

    private AttributeFactory() {
        throw new IllegalStateException("Utility class");
    }

    public static Attribute create(CreateAttributeCommand command) {
        return Attribute.builder()
                .name(command.getName())
                .categoryId(command.getCategoryId())
                .description(command.getDescription())
                .attributeFormat(command.getAttributeFormat())
                .status(AttributeStatus.ACTIVE)
                .build();
    }
}

