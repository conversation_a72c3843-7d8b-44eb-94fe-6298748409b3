package com.mercaso.ims.infrastructure.process.parser;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.infrastructure.excel.data.JetroDailyUpdatedData;
import com.mercaso.ims.infrastructure.excel.processor.JetroItemDailyUpdateSheetProcessor;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class JetroItemCostCollectionParser implements ItemCostCollectionParser {


    private final ItemCostCollectionService itemCostCollectionService;
    private final JetroItemDailyUpdateSheetProcessor jetroItemDailyUpdateSheetProcessor;


    @Override
    public List<ItemCostCollectionItemParsingResultDto> parse(UUID itemCostCollectionId) {
        ItemCostCollection itemCostCollection = itemCostCollectionService.findById(itemCostCollectionId);

        if (null == itemCostCollection) {
            return new ArrayList<>();
        }
        List<JetroDailyUpdatedData> sevenStarItemDailyUpdatedDataList = jetroItemDailyUpdateSheetProcessor.process(
            itemCostCollection.getFileName());
        return sevenStarItemDailyUpdatedDataList.stream().map(this::convertToItemCostCollectionItemParsingResultDto).toList();

    }


    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return VendorConstant.JETRO.equals(vendorName) && !sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    @Override
    public boolean isUpdateAvailability() {
        return true;
    }


    private ItemCostCollectionItemParsingResultDto convertToItemCostCollectionItemParsingResultDto(JetroDailyUpdatedData data) {
        return ItemCostCollectionItemParsingResultDto.builder()
            .upc(data.getUpc())
            .cost(data.getCost())
            .vendorItemName(data.getName())
            .vendorSkuNumber(data.getItemNumber())
            .availability(data.getAvailability())
            .aisle(data.getBin())
            .build();
    }

}