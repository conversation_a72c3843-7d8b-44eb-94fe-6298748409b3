package com.mercaso.wms.application.queryservice;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.mercaso.ims.client.api.QueryItemRestApiApi;
import com.mercaso.wms.application.dto.VendorItemDto;
import com.mercaso.wms.domain.vendoritem.VendorItem;
import com.mercaso.wms.domain.vendoritem.VendorItemRepository;
import com.mercaso.wms.utils.MockDataUtils;
import java.io.ByteArrayInputStream;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

class VendorItemQueryServiceTest {

    private final VendorItemRepository vendorItemRepository = mock(VendorItemRepository.class);

    private final QueryItemRestApiApi queryItemRestApi = mock(QueryItemRestApiApi.class);

    private final VendorItemQueryService vendorItemQueryService = new VendorItemQueryService(vendorItemRepository,
        queryItemRestApi);

    @Test
    void when_find_vendorItem_by_id_then_return_vendorItemDto() {
        when(vendorItemRepository.findById(any())).thenReturn(MockDataUtils.buildVendorItem(UUID.randomUUID(),
            UUID.randomUUID()));

        assertNotNull(vendorItemQueryService.findById(UUID.randomUUID()));
    }

    @Test
    void when_find_vendorItems_by_warehouseId_then_return_empty_list() {
        when(vendorItemRepository.findByWarehouseIdAndSkuNumbers(any(), any(), any())).thenReturn(Page.empty());
        Page<VendorItemDto> vendorItems = vendorItemQueryService.findVendorItems(UUID.randomUUID(), null, null);
        assertEquals(0, vendorItems.getTotalElements());
        assertEquals(0, vendorItems.getContent().size());
    }

    @Test
    void when_find_vendorItems_by_warehouseId_and_skuNumbers_then_return_vendorItemDtos() {
        Page<VendorItem> page = new PageImpl<>(List.of(MockDataUtils.buildVendorItem(UUID.randomUUID(), UUID.randomUUID())));

        when(vendorItemRepository.findByWarehouseIdAndSkuNumbers(any(), any(), any())).thenReturn(page);
        Page<VendorItemDto> vendorItems = vendorItemQueryService.findVendorItems(UUID.randomUUID(), List.of("JC1111"), null);
        assertEquals(1, vendorItems.getTotalElements());
        assertEquals(1, vendorItems.getContent().size());
    }

    @Test
    void when_export_with_valid_warehouseId_and_skuNumbers_then_return_excel_stream() {
        List<VendorItem> vendorItemDtos = List.of(MockDataUtils.buildVendorItem(UUID.randomUUID(), UUID.randomUUID()));
        when(vendorItemRepository.findByWarehouseIdAndSkuNumbers(any(), any(), any())).thenReturn(new PageImpl<>(vendorItemDtos));

        ByteArrayInputStream result = vendorItemQueryService.export(UUID.randomUUID(), List.of("JC1111"));
        assertNotNull(result);
    }

    @Test
    void when_export_with_null_warehouseId_and_empty_skuNumbers_then_return_excel_stream() {
        List<VendorItem> vendorItems = List.of(MockDataUtils.buildVendorItem(UUID.randomUUID(), UUID.randomUUID()));
        when(vendorItemRepository.findAll()).thenReturn(vendorItems);

        ByteArrayInputStream result = vendorItemQueryService.export(null, List.of());
        assertNotNull(result);
    }

}