package com.mercaso.wms.delivery.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mercaso.wms.delivery.application.command.UploadDocumentCommand;
import com.mercaso.wms.delivery.application.dto.document.DocumentDto;
import com.mercaso.wms.delivery.application.service.DocumentApplicationService;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

@ExtendWith(MockitoExtension.class)
class DeliveryDocumentResourceTest {

    private final DocumentApplicationService documentApplicationService = mock(DocumentApplicationService.class);
    private final DeliveryDocumentResource deliveryDocumentResource = new DeliveryDocumentResource(documentApplicationService);

    private UUID deliveryOrderId;
    private MockMultipartFile file;
    private DocumentDto documentDto;

    @BeforeEach
    void setUp() {
        deliveryOrderId = UUID.randomUUID();
        file = new MockMultipartFile(
            "file",
            "test.pdf",
            "application/pdf",
            "test content".getBytes()
        );

        documentDto = DocumentDto.builder()
            .id(UUID.randomUUID())
            .fileName("test.pdf")
            .fileUrl("https://example.com/test.pdf")
            .build();
    }

    @Test
    void uploadDocuments_WithValidData_ShouldUploadDocument() throws Exception {
        // Given
        when(documentApplicationService.uploadDocuments(any(UploadDocumentCommand.class), any()))
            .thenReturn(List.of(documentDto));

        // When
        List<DocumentDto> result = deliveryDocumentResource.uploadDocuments(UploadDocumentCommand.builder()
            .entityId(deliveryOrderId)
            .build(), List.of(file).toArray(new MultipartFile[0]));

        // Then
        assertNotNull(result);
        assertEquals(documentDto.getId(), result.getFirst().getId());
        assertEquals(documentDto.getFileName(), result.getFirst().getFileName());
        assertEquals(documentDto.getFileUrl(), result.getFirst().getFileUrl());

        verify(documentApplicationService).uploadDocuments(any(UploadDocumentCommand.class), any());
    }

} 