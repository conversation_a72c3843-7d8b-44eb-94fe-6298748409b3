const {spawn} = require('child_process');
const path = require('path');
const fs = require("fs");
const os = require('os');

let cachedChromePath = null;
// Add a browser pool for reuse
let browserPool = null;
let isPoolInitializing = false;
const MAX_POOL_SIZE = 2;

function log(message, level = 'info') {
	console[level](`[PDF-GEN] ${message}`);
}

// Initialize browser pool
async function initBrowserPool() {
    if (browserPool || isPoolInitializing) return;
    
    isPoolInitializing = true;
    try {
        const puppeteer = require("puppeteer-core");
        
        let executablePath = cachedChromePath;
        if (!executablePath) {
            const platform = os.platform();
            const paths = platform === 'darwin' ? 
                ['/Applications/Google Chrome.app/Contents/MacOS/Google Chrome', '/Applications/Chromium.app/Contents/MacOS/Chromium'] : 
                ['/usr/bin/chromium', '/usr/bin/chromium-browser', '/usr/bin/google-chrome', '/usr/bin/google-chrome-stable', '/opt/google/chrome/chrome'];
            
            for (const p of paths) {
                if (fs.existsSync(p)) {
                    executablePath = p;
                    cachedChromePath = p;
                    break;
                }
            }
        }
        
        const launchOptions = {
            headless: "new",
            args: [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-setuid-sandbox',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-component-extensions-with-background-pages',
                '--disable-default-apps',
                '--mute-audio',
                '--no-default-browser-check',
                '--no-first-run',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-background-timer-throttling',
                '--disable-background-networking'
            ]
        };
        
        if (executablePath) {
            launchOptions.executablePath = executablePath;
        }
        
        browserPool = [];
        // Only create one browser instance on initialization to avoid memory issues
        const browser = await puppeteer.launch(launchOptions);
        browserPool.push(browser);
        log(`Browser pool initialized`);
    } catch (error) {
        log(`Failed to initialize browser pool: ${error}`, 'error');
    } finally {
        isPoolInitializing = false;
    }
}

// Close all browsers
async function closeBrowserPool() {
    if (!browserPool) return;
    
    for (const browser of browserPool) {
        try {
            await browser.close();
        } catch (e) {}
    }
    browserPool = null;
}

// Get browser from pool or create new one
async function getBrowser() {
    if (!browserPool && !isPoolInitializing) {
        await initBrowserPool();
    }
    
    if (browserPool && browserPool.length > 0) {
        return browserPool.pop();
    }
    
    // If pool is being initialized or no browsers available, create a new one
    log('Creating new browser instance');
    const puppeteer = require("puppeteer-core");
    
    let executablePath = cachedChromePath;
    const launchOptions = {
        headless: "new",
        args: [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-setuid-sandbox',
            '--disable-gpu',
            '--disable-extensions',
            '--disable-component-extensions-with-background-pages',
            '--disable-default-apps',
            '--mute-audio',
            '--no-default-browser-check',
            '--no-first-run',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-background-timer-throttling',
            '--disable-background-networking'
        ]
    };
    
    if (executablePath) {
        launchOptions.executablePath = executablePath;
    }
    
    return await puppeteer.launch(launchOptions);
}

// Return browser to pool or close it
async function returnBrowser(browser) {
    if (browserPool && browserPool.length < MAX_POOL_SIZE) {
        browserPool.push(browser);
    } else {
        try {
            await browser.close();
        } catch (e) {}
    }
}

async function runPupeteer(html, numberOfPreviousPages, orderNumber) {	
	const tempDir = os.tmpdir();
	const uniqueId = `${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
	const htmlFile = path.join(tempDir, `${orderNumber}_${uniqueId}.html`);
	
	const cleanupResources = () => {
		try {
			if (fs.existsSync(htmlFile)) {
				fs.unlinkSync(htmlFile);
			}
		} catch (e) {}
	};
	
	try {
		fs.writeFileSync(htmlFile, html, 'utf8');
		
		return await new Promise((resolve, reject) => {
			try {
				const timeoutLimit = 30000;
				
				try {
					log('Initializing puppeteer for PDF generation');
					
					(async function() {
						const browser = await getBrowser();
						
						try {
							const {pdfPage} = require("./report/report");
							
							const page = await browser.newPage();
							await page.setCacheEnabled(true);
							
							await page.setRequestInterception(true);
							page.on('request', (request) => {
								const resourceType = request.resourceType();
								if (['image', 'media', 'font', 'stylesheet'].includes(resourceType)) {
									request.continue();
								} else if (['script', 'websocket', 'xhr', 'fetch', 'eventsource', 'manifest'].includes(resourceType)) {
									request.abort();
								} else {
									request.continue();
								}
							});
							
							const content = fs.readFileSync(htmlFile, 'utf8');
							await page.setContent(content, { 
								waitUntil: 'networkidle0',
								timeout: timeoutLimit
							});
							
							await page.evaluate(() => {
								const elements = document.querySelectorAll('script, iframe');
								for (const el of elements) {
									el.remove();
								}
								
								const images = document.querySelectorAll('img');
								for (const img of images) {
									img.style.maxWidth = '600px';
									img.style.height = 'auto';
								}
							});
							
							const pdfPath = path.resolve(__dirname, `./${orderNumber}.pdf`);
							
							await pdfPage(page, {
								scale: 1,
								path: pdfPath,
								format: 'letter',
								margin: {top: "50px", bottom: "80px", left: "24px", right: "24px"},
								displayHeaderFooter: false,
								printBackground: false,
								preferCSSPageSize: true,
								timeout: timeoutLimit
							});
							
							await page.close();
							await returnBrowser(browser);
							
							if (fs.existsSync(pdfPath)) {
								let pageCount = 1;
								try {
									const pdfParse = require('pdf-parse');
									const pdfData = await pdfParse(fs.readFileSync(pdfPath), { max: 0 });
									pageCount = pdfData.numpages || 1;
								} catch (e) {
									log('Error parsing PDF: ' + e.message, 'error');
								}
								cleanupResources();
								resolve(JSON.stringify({success: true, pagesNumber: pageCount}));
							} else {
								throw new Error('PDF file was not created');
							}
						} catch (pageErr) {
							await returnBrowser(browser);
							throw pageErr;
						}
					})();
				} catch (directErr) {
					cleanupResources();
					reject(directErr);
				}
			} catch (err) {
				cleanupResources();
				reject(err);
			}
		});
	} catch (err) {
		cleanupResources();
		throw err;
	}
}

// Initialize browser pool on module load (but with less aggressive initialization)
setTimeout(() => {
    initBrowserPool().catch(err => {
        log(`Failed to initialize browser pool on load: ${err}`, 'error');
    });
}, 1000);

// Add cleanup function for process exit
process.on('exit', () => {
    if (browserPool) {
        for (const browser of browserPool) {
            try {
                browser.close();
            } catch (e) {}
        }
    }
});

async function createAutomaticInvoice(html, numberOfPreviousPages, orderNumber) {
	try {
		const resData = await runPupeteer(html, numberOfPreviousPages??0, orderNumber);
		let parsed;
		try {
			parsed = JSON.parse(resData);
		} catch (e) {
			throw new Error("PDF generation failed: could not parse response");
		}
		
		if (parsed.success !== true) {
			throw new Error("PDF generation failed: " + (parsed.error || "unknown error"));
		}

		return parsed.pagesNumber;
	} catch (e) {
		throw new Error("PDF generation failed: " + e.message);
	}
}

module.exports = {createAutomaticInvoice}

