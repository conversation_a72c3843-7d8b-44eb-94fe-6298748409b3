Alter Table inventory_stock_history
    add column entity_id uuid;
Alter Table inventory_stock_history
    add column entity_name varchar(100);

Alter Table inventory_stock_history
    rename column stock_id to from_stock_id;
Alter Table inventory_stock_history
    rename column warehouse_id to to_stock_id;
Alter Table inventory_stock_history
    rename column location_id to from_location_id;
Alter Table inventory_stock_history
    rename column location_name to to_location_id;

ALTER TABLE inventory_stock_history
    ALTER COLUMN to_location_id TYPE UUID USING to_location_id::UUID;
ALTER TABLE inventory_stock_history
    ALTER COLUMN from_stock_id DROP NOT NULL;
ALTER TABLE inventory_stock_history
    ALTER COLUMN to_stock_id DROP NOT NULL;

comment on column inventory_stock_history.entity_id is 'Entity ID: pickingTaskItemId';
comment on column inventory_stock_history.entity_name is 'Entity Name: PICKING_TASK_ITEM';
