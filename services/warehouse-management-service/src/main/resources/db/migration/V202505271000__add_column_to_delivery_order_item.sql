ALTER TABLE da_delivery_order_items
    ADD COLUMN if not exists discount_allocations varchar DEFAULT null;
ALTER TABLE da_delivery_order
    ADD COLUMN if not exists current_total_discounts numeric(15, 2) DEFAULT 0.0;
ALTER TABLE da_delivery_order
    ADD COLUMN if not exists discount_applications varchar DEFAULT NULL;

comment on column da_delivery_order_items.discount_allocations is 'discount applied to the delivery order item';
comment on column da_delivery_order.current_total_discounts is 'Total discounts applied to the delivery order';
comment on column da_delivery_order.discount_applications is 'Comma-separated list of discount codes applied to the delivery order';