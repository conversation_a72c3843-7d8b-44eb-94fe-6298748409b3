package com.mercaso.wms.infrastructure.repository.batch.jpa;

import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface BatchJpaDao extends JpaRepository<BatchDo, UUID> {

    List<BatchDo> findByTag(@NotNull String tag);

    @Query("SELECT b FROM BatchDo b WHERE " +
        "(b.deletedAt IS NULL) AND " +
        "(:deliveryDate IS NULL OR b.tag = :deliveryDate) AND " +
        "(:updatedBy IS NULL OR b.updatedBy = :updatedBy) AND " +
        "(:createdBy IS NULL OR b.createdBy = :createdBy) " +
        "ORDER BY b.createdAt DESC")
    Page<BatchDo> searchBatchDo(@Param("deliveryDate") String deliveryDate,
        @Param("updatedBy") String updatedBy,
        @Param("createdBy") String createdBy,
        Pageable pageable);


    @Query("SELECT b FROM BatchDo b WHERE b.tag = :tag AND (b.finaleTransferShipmentNumber IS NULL OR b.finaleTransferShipmentNumber = '')")
    List<BatchDo> findUntransferredBatches(@Param("tag") String tag);

}
