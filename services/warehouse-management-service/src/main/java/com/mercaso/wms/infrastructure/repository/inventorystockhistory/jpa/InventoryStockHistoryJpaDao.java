package com.mercaso.wms.infrastructure.repository.inventorystockhistory.jpa;

import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.repository.inventorystockhistory.jpa.dataobject.InventoryStockHistoryDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface InventoryStockHistoryJpaDao extends JpaRepository<InventoryStockHistoryDo, UUID> {

    List<InventoryStockHistoryDo> findByEntityIdAndEntityName(UUID entityId, EntityEnums entityName);

}
