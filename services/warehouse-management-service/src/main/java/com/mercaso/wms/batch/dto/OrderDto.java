package com.mercaso.wms.batch.dto;

import com.mercaso.wms.batch.enums.ColourMarkingEnum;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import java.time.Instant;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDto {

    private UUID id;
    private List<String> tags;
    private String orderNumber;
    private String title;
    private String sku;
    private String itemName;
    private Integer quantity;
    private Instant createdAt;
    private Instant updatedAt;
    private String colourMarking;

    public static List<OrderDto> toOrderDtoLists(List<ShippingOrder> shippingOrders,
        List<DeliveryDateChangeDto> deliveryDateChangeDtos) {
        List<OrderDto> orderDtoList = new LinkedList<>();
        List<OrderDto> firstOrderList = new LinkedList<>();
        for (ShippingOrder shippingOrder : shippingOrders) {
            for (ShippingOrderItem lineItem : shippingOrder.getShippingOrderItems()) {
                OrderDto orderDto = OrderDto.builder()
                    .id(shippingOrder.getId())
                    .orderNumber(shippingOrder.getOrderNumber())
                    .sku(lineItem.getSkuNumber())
                    .itemName(lineItem.getTitle())
                    .quantity(lineItem.getQty())
                    .createdAt(shippingOrder.getCreatedAt())
                    .updatedAt(shippingOrder.getUpdatedAt())
                    .title(lineItem.getTitle())
                    .build();
                if (!CollectionUtils.isEmpty(deliveryDateChangeDtos) && deliveryDateChangeDtos.stream()
                    .anyMatch(deliveryDateChangeDto -> shippingOrder.getOrderNumber()
                        .contains(deliveryDateChangeDto.getOrderNumber()))) {
                    orderDto.setColourMarking(ColourMarkingEnum.GREEN.name());
                    firstOrderList.add(orderDto);
                } else {
                    orderDtoList.add(orderDto);
                }
            }
        }
        if (!firstOrderList.isEmpty()) {
            orderDtoList.addAll(0, firstOrderList);
        }
        return orderDtoList;
    }

}
