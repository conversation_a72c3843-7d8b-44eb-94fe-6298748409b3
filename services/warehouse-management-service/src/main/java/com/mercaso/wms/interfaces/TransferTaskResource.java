package com.mercaso.wms.interfaces;


import com.mercaso.wms.application.command.transfertask.CreateTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.LoadTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.ReceiveTransferTaskCommand;
import com.mercaso.wms.application.command.transfertask.UpdateTransferTaskCommand;
import com.mercaso.wms.application.dto.transfertask.TransferTaskDto;
import com.mercaso.wms.application.service.TransferTaskApplicationService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/transfer-tasks")
@RequiredArgsConstructor
public class TransferTaskResource {

    private final TransferTaskApplicationService transferTaskApplicationService;

    @PreAuthorize("hasAuthority('wms:read:transfer-tasks')")
    @GetMapping("/{id}")
    public TransferTaskDto getTransferTask(@PathVariable UUID id) {
        return transferTaskApplicationService.getTransferTask(id);
    }

    @PreAuthorize("hasAuthority('wms:write:transfer-tasks')")
    @PostMapping
    public TransferTaskDto createTransferTask(@RequestBody CreateTransferTaskCommand command) {
        log.info("Create TransferTask: {}", command);
        return transferTaskApplicationService.createTransferTask(command);
    }

    @PreAuthorize("hasAuthority('wms:write:transfer-tasks')")
    @PutMapping
    public TransferTaskDto updateTransferTask(@RequestBody UpdateTransferTaskCommand command) {
        log.info("Updating transfer task: {}", command.getTransferTaskId());
        return transferTaskApplicationService.updateTransferTask(command);
    }

    @PreAuthorize("hasAuthority('wms:write:transfer-tasks')")
    @PutMapping("/{id}/activate")
    public TransferTaskDto activateTransferTask(@PathVariable UUID id) {
        log.info("Activating transfer task with id: {}", id);
        return transferTaskApplicationService.activate(id);
    }

    @PreAuthorize("hasAuthority('wms:write:transfer-tasks')")
    @PutMapping("/{id}/load")
    public TransferTaskDto loadTransferTask(
        @PathVariable UUID id,
        @RequestBody LoadTransferTaskCommand command) {
        log.info("Loading transfer task with id: {}", id);
        return transferTaskApplicationService.load(id, command);
    }

    @PreAuthorize("hasAuthority('wms:write:transfer-tasks')")
    @PutMapping("/{id}/receive")
    public TransferTaskDto receiveTransferTask(
        @PathVariable UUID id,
        @RequestBody ReceiveTransferTaskCommand command) {
        log.info("Receiving transfer task with id: {}", id);
        return transferTaskApplicationService.receive(id, command);
    }

    @PreAuthorize("hasAuthority('wms:write:transfer-tasks')")
    @PutMapping("/{id}/revert-to-draft")
    public TransferTaskDto revertToDraft(@PathVariable UUID id) {
        log.info("Reverting transfer task with id: {}", id);
        return transferTaskApplicationService.revertToDraft(id);
    }

}
