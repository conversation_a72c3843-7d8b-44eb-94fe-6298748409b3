package com.mercaso.wms.delivery.application.dto.view;

import com.mercaso.wms.application.dto.BaseDto;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchAccountView extends BaseDto {

    private UUID id;
    private String warehouseName;
    private String status;
    private UUID userId;
    private String userName;
    private String email;
    private boolean hasSecretKey;
    private Instant createdAt;
    private String createdBy;
    private Instant updatedAt;
    private String updatedBy;
}
