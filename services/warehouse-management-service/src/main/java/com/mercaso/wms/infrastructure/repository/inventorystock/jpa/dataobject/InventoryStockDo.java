package com.mercaso.wms.infrastructure.repository.inventorystock.jpa.dataobject;

import com.mercaso.wms.domain.inventorystock.enums.InventoryStockStatus;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import com.mercaso.wms.infrastructure.repository.location.jpa.dataobject.LocationDo;
import com.mercaso.wms.infrastructure.repository.warehouse.jpa.dataobject.WarehouseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "inventory_stock")
@SQLDelete(sql = "update inventory_stock set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class InventoryStockDo extends BaseDo {

    @JoinColumn(name = "warehouse_id", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private WarehouseDo warehouse;

    @Column(name = "sku_number", length = 20)
    private String skuNumber;

    @Column(length = 200)
    private String title;

    @Column(name = "item_id")
    private UUID itemId;

    @JoinColumn(name = "location_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private LocationDo location;

    @Column(name = "lot_number", length = 50)
    private String lotNumber;

    @Column(name = "production_date")
    private LocalDate productionDate;

    @Column(name = "expiration_date")
    private LocalDate expirationDate;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal qty = BigDecimal.ZERO;

    @Column(name = "reserved_qty", nullable = false, precision = 10, scale = 2)
    private BigDecimal reservedQty = BigDecimal.ZERO;

    @Column(name = "available_qty", nullable = false, precision = 10, scale = 2)
    private BigDecimal availableQty = BigDecimal.ZERO;

    @Column(length = 20)
    @Enumerated(value = EnumType.STRING)
    private InventoryStockStatus status;

    @Column(name = "lpn_number", length = 50)
    private String lpnNumber;

    @Column(name = "vendor_id")
    private UUID vendorId;

}
