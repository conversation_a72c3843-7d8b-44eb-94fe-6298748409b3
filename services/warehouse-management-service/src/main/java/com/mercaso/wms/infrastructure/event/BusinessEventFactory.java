package com.mercaso.wms.infrastructure.event;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.businessevents.dto.BusinessEventDto;
import com.mercaso.businessevents.dto.BusinessEventEntityDto;
import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.application.dto.event.BusinessEventPayloadDto;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.businessevent.EventTypeEnums;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BusinessEventFactory {

    public static BusinessEventDto build(BusinessEventPayloadDto<? extends BaseDto> payloadDto) {
        EventTypeEnums eventTypeEnums = EventTypeEnums.forPayload(payloadDto.getClass());
        if (Objects.isNull(eventTypeEnums)) {
            return null;
        }
        JsonNode payload = SerializationUtils.toTree(payloadDto);
        return BusinessEventDto.builder()
            .entities(buildEntities(eventTypeEnums.getEntities(), payload))
            .type(eventTypeEnums.name())
            .payload(payload)
            .build();
    }

    private static List<BusinessEventEntityDto> buildEntities(List<EntityEnums> entities, JsonNode payload) {
        ArrayList<BusinessEventEntityDto> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(entities)) {
            return list;
        }
        for (EntityEnums entity : entities) {
            JsonNode jsonNode = payload.get(entity.getIdField());
            if (jsonNode == null) {
                continue;
            }
            list.add(BusinessEventEntityDto.builder()
                .entityId(jsonNode.asText())
                .entityType(entity.getValue())
                .build());
        }
        return list;
    }

}
