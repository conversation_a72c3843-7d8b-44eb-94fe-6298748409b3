package com.mercaso.wms.application.command.pickingtask;

import com.mercaso.wms.application.command.BaseCommand;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BulkCompletePickingTaskCommand extends BaseCommand {

    @NotEmpty
    private List<UUID> pickingTaskIds;

}


