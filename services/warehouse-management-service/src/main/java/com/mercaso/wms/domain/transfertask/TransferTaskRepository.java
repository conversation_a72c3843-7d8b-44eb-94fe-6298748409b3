package com.mercaso.wms.domain.transfertask;

import com.mercaso.wms.application.dto.view.SearchTransferTaskView;
import com.mercaso.wms.application.query.TransferTaskQuery;
import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TransferTaskRepository extends BaseDomainRepository<TransferTask, UUID> {

    Page<SearchTransferTaskView> search(TransferTaskQuery transferTaskQuery, Pageable pageable);

    void deleteAll();

    List<TransferTask> saveAll(List<TransferTask> transferTasks);

    List<TransferTask> findByPickingTaskItemId(UUID pickingTaskItemId);

}
