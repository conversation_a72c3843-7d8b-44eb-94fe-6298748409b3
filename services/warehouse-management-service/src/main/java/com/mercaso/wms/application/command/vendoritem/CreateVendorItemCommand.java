package com.mercaso.wms.application.command.vendoritem;

import com.mercaso.wms.application.command.BaseCommand;
import com.mercaso.wms.domain.vendoritem.enums.VendorItemStatus;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class CreateVendorItemCommand extends BaseCommand {

    private UUID warehouseId;

    private UUID itemId;

    private String skuNumber;

    private String title;

    private VendorItemStatus status;

    private String from;

    private String aisle;

}
