package com.mercaso.wms.application.searchservice;

import com.mercaso.wms.application.dto.LocationDto;
import com.mercaso.wms.application.mapper.location.LocationDtoApplicationMapper;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocationSearchService {

    private final LocationCache locationCache;

    private final LocationDtoApplicationMapper locationDtoApplicationMapper;

    public Page<LocationDto> search(String locationName, Pageable pageable) {
        Map<UUID, Location> locationMap = locationCache.getLocationMap();
        if (locationMap.isEmpty()) {
            log.warn("Location cache is empty");
            return Page.empty(pageable);
        }

        List<Location> filteredList = StringUtils.isNotEmpty(locationName)
            ? locationMap.values().stream()
            .filter(location -> location.getName().toUpperCase().contains(locationName.toUpperCase()))
            .collect(Collectors.toList())
            : locationMap.values().stream().toList();

        int total = filteredList.size();
        long start = Math.min(pageable.getOffset(), total);
        long end = Math.min(start + pageable.getPageSize(), total);
        if (start >= end) {
            return new PageImpl<>(Collections.emptyList(), pageable, total);
        }
        List<Location> paginatedList = filteredList.subList((int) start, (int) end);

        List<LocationDto> locationDtos = locationDtoApplicationMapper.domainToDtos(paginatedList);
        return new PageImpl<>(locationDtos, pageable, total);
    }
}