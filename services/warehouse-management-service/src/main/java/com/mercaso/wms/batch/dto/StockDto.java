package com.mercaso.wms.batch.dto;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.location.enums.LocationType;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;


@Data
@Builder
public class StockDto {

    private UUID locationId;

    private String subLocation;

    private String name;

    private LocationType type;

    private String sku;

    private int totalQty;

    private int remainingQty;

    private SourceEnum source;


}
