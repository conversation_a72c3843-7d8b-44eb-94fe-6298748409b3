package com.mercaso.wms.infrastructure.statemachine.config;

import static com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus.DRAFT;
import static com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus.IN_TRANSIT;
import static com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus.READY_TO_LOAD;
import static com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus.RECEIVED;

import com.mercaso.wms.domain.transfertask.TransferTask;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskStatus;
import com.mercaso.wms.domain.transfertask.enums.TransferTaskTransitionEvents;
import com.mercaso.wms.infrastructure.statemachine.StatemachineFactory;
import com.mercaso.wms.infrastructure.statemachine.factory.WmsStateMachineFactory;
import java.util.EnumSet;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.statemachine.StateMachinePersist;
import org.springframework.statemachine.config.EnableStateMachineFactory;
import org.springframework.statemachine.config.EnumStateMachineConfigurerAdapter;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.persist.DefaultStateMachinePersister;
import org.springframework.statemachine.persist.StateMachinePersister;

@Configuration
@EnableStateMachineFactory(contextEvents = false, name = "transferTaskTransitionEventsStateMachineFactory")
public class TransferTaskStateMachineConfig extends
    EnumStateMachineConfigurerAdapter<TransferTaskStatus, TransferTaskTransitionEvents> {

    @Override
    public void configure(StateMachineStateConfigurer<TransferTaskStatus, TransferTaskTransitionEvents> states)
        throws Exception {
        states
            .withStates()
            .initial(DRAFT)
            .states(EnumSet.allOf(TransferTaskStatus.class))
        ;
    }

    @Override
    public void configure(StateMachineTransitionConfigurer<TransferTaskStatus, TransferTaskTransitionEvents> transitions)
        throws Exception {
        transitions
            .withExternal()
            .source(DRAFT).target(READY_TO_LOAD)
            .event(TransferTaskTransitionEvents.ACTIVATED)
            .and()
            .withExternal()
            .source(DRAFT).target(IN_TRANSIT)
            .event(TransferTaskTransitionEvents.LOADED)
            .and()
            .withExternal()
            .source(READY_TO_LOAD).target(IN_TRANSIT)
            .event(TransferTaskTransitionEvents.LOADED)
            .and()
            .withExternal()
            .source(IN_TRANSIT).target(RECEIVED)
            .event(TransferTaskTransitionEvents.RECEIVED)
            .and()
            .withExternal()
            .source(READY_TO_LOAD).target(DRAFT)
            .event(TransferTaskTransitionEvents.REVERT_TO_DRAFT);
    }

    @Bean
    @StatemachineFactory(domainClass = TransferTask.class)
    public WmsStateMachineFactory<TransferTaskStatus, TransferTaskTransitionEvents, TransferTask> transferTaskStateMachineAdapter(
        StateMachineFactory<TransferTaskStatus, TransferTaskTransitionEvents> transferTaskTransitionEventsStateMachineFactory,
        StateMachinePersister<TransferTaskStatus, TransferTaskTransitionEvents, TransferTask> transferTaskStateMachinePersister) {
        return new WmsStateMachineFactory<>(transferTaskTransitionEventsStateMachineFactory, transferTaskStateMachinePersister);
    }

    @Bean
    public StateMachinePersister<TransferTaskStatus, TransferTaskTransitionEvents, TransferTask> transferTaskStateMachinePersister(
        StateMachinePersist<TransferTaskStatus, TransferTaskTransitionEvents, TransferTask> transferTaskStateMachinePersist) {
        return new DefaultStateMachinePersister<>(transferTaskStateMachinePersist);
    }

}
