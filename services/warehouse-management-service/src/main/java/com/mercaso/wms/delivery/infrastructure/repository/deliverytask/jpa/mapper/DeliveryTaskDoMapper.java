package com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.mapper;

import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.mapper.DeliveryOrderItemDoMapper;
import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
    uses = DeliveryOrderItemDoMapper.class,
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryTaskDoMapper extends BaseDoMapper<DeliveryTaskDo, DeliveryTask> {

}