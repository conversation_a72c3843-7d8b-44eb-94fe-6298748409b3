package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AccountPreferenceUpdatedPayloadDto extends BusinessEventPayloadDto<AccountPreferenceDto> {

    private UUID accountPreferenceId;

    @Builder
    public AccountPreferenceUpdatedPayloadDto(AccountPreferenceDto data, UUID accountPreferenceId) {
        super(data);
        this.accountPreferenceId = accountPreferenceId;
    }

}
