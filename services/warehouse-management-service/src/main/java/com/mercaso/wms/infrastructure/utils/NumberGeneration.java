package com.mercaso.wms.infrastructure.utils;

import com.mercaso.wms.delivery.infrastructure.repository.deliverytask.jpa.dataobject.DeliveryTaskDo;
import com.mercaso.wms.infrastructure.repository.batch.jpa.dataobject.BatchDo;
import com.mercaso.wms.infrastructure.repository.crossdock.jpa.dataobject.CrossDockTaskDo;
import com.mercaso.wms.infrastructure.repository.pickingtask.jpa.dataobject.PickingTaskDo;
import com.mercaso.wms.infrastructure.repository.receivingtask.jpa.dataobject.ReceivingTaskDo;
import com.mercaso.wms.infrastructure.repository.transfertask.jpa.dataobject.TransferTaskDo;
import java.security.SecureRandom;
import java.util.EnumSet;
import org.hibernate.FlushMode;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.generator.BeforeExecutionGenerator;
import org.hibernate.generator.EventType;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NumberGeneration implements BeforeExecutionGenerator {

    private static final Logger log = LoggerFactory.getLogger(NumberGeneration.class);

    private static final String CHAR_POOL = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    @Override
    public Object generate(SharedSessionContractImplementor session, Object owner, Object currentValue, EventType eventType) {
        if (owner instanceof BatchDo) {
            return generateNumber(session, "B-", "BatchDo");
        }
        if (owner instanceof PickingTaskDo) {
            return generateNumber(session, "P-", "PickingTaskDo");
        }
        if (owner instanceof ReceivingTaskDo) {
            return generateNumber(session, "R-", "ReceivingTaskDo");
        }
        if (owner instanceof TransferTaskDo) {
            return generateNumber(session, "T-", "TransferTaskDo");
        }
        if (owner instanceof DeliveryTaskDo) {
            return generateNumber(session, "D-", "DeliveryTaskDo");
        }
        if (owner instanceof CrossDockTaskDo) {
            return generateNumber(session, "C-", "CrossDockTaskDo");
        }
        return null;
    }

    private static String generateNumber(SharedSessionContractImplementor session, String prefix, String propertyName) {
        String sql = "SELECT count(1) FROM " + propertyName + " WHERE number = :number";
        Query<Long> query = session.createQuery(sql, Long.class);
        query.setHibernateFlushMode(FlushMode.COMMIT);
        String number;
        while (true) {
            number = generateString(prefix);
            Long result = query.setParameter("number", number).uniqueResult();
            if (result == 0) {
                break;
            } else {
                log.warn("Number already exists: {}", number);
            }
        }
        return number;
    }

    public static String generateString(String prefix) {
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder sb = new StringBuilder(7);
        for (int i = 0; i < 7; i++) {
            int index = secureRandom.nextInt(CHAR_POOL.length());
            sb.append(CHAR_POOL.charAt(index));
        }
        return prefix + sb;
    }

    public static String generateSecureRandomNumeric(int length) {
        SecureRandom secureRandom = new SecureRandom();
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(secureRandom.nextInt(10)); // 0-9
        }
        return sb.toString();
    }

    @Override
    public EnumSet<EventType> getEventTypes() {
        return EnumSet.of(EventType.INSERT);
    }
}
