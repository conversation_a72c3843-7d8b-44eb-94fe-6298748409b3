package com.mercaso.wms.application.service;

import static com.mercaso.wms.infrastructure.external.ums.UmsAdaptor.PICKER_ROLE;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.user.client.dto.CreateUserRequest;
import com.mercaso.user.client.dto.UpdateUserRequest;
import com.mercaso.user.client.dto.UserDto;
import com.mercaso.wms.application.command.accountpreference.CreateAccountPreferenceCommand;
import com.mercaso.wms.application.command.accountpreference.UpdateAccountPreferenceCommand;
import com.mercaso.wms.application.dto.accountpreference.AccountPreferenceDto;
import com.mercaso.wms.application.dto.event.AccountPreferenceCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.AccountPreferenceUpdatedPayloadDto;
import com.mercaso.wms.application.mapper.accountpreference.AccountPreferenceDtoApplicationMapper;
import com.mercaso.wms.domain.accountpreference.AccountPreference;
import com.mercaso.wms.domain.accountpreference.AccountPreferenceRepository;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.domain.warehouse.WarehouseRepository;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.external.ums.UmsAdaptor;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import com.mercaso.wms.infrastructure.utils.RandomKeyGenerationUtils;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountPreferenceApplicationService {

    private final AccountPreferenceRepository accountPreferenceRepository;

    private final WarehouseRepository warehouseRepository;

    private final BusinessEventDispatcher businessEventDispatcher;

    private final AccountPreferenceDtoApplicationMapper accountPreferenceDtoApplicationMapper;

    private final UmsAdaptor umsAdaptor;

    public AccountPreferenceDto createAccountPreference(CreateAccountPreferenceCommand command) {
        validateAccountPreferenceNotExists(command.getEmail());

        Warehouse workWarehouse = getWarehouse(command.getWorkWarehouseId(), "Work warehouse not found.");
        Warehouse externalWarehouse = getWarehouse(command.getExternalWarehouseId(), "External warehouse not found.");

        AccountPreference accountPreference;
        if (command.getUserId() == null) {
            String secretKey = RandomKeyGenerationUtils.generateSecureRandomKey();
            UUID userId = createUserIfUserIdIdIsNull(command, secretKey);
            accountPreference = AccountPreference.builder()
                .build()
                .createNew(command, userId, secretKey, workWarehouse, externalWarehouse);
        } else {
            umsAdaptor.assignPickerRolesToUser(command.getUserId());
            accountPreference = AccountPreference.builder()
                .build()
                .create(command, workWarehouse, externalWarehouse);
        }
        AccountPreferenceDto accountPreferenceDto = mapToDto(accountPreferenceRepository.save(accountPreference));

        dispatchCreatedEvent(accountPreferenceDto);

        return accountPreferenceDto;
    }

    private UUID createUserIfUserIdIdIsNull(CreateAccountPreferenceCommand command, String secretKey) {
        try {
            CreateUserRequest request = new CreateUserRequest();
            request.setEmail(command.getEmail());
            request.setName(command.getUserName());
            request.setPassword(secretKey);
            UserDto userDto = umsAdaptor.createUser(request, PICKER_ROLE);
            if (userDto != null) {
                return userDto.getId();
            } else {
                throw new WmsBusinessException(String.format("Create picker %s error.", command.getUserName()));
            }
        } catch (Exception e) {
            throw new WmsBusinessException("Create picker error.", e);
        }
    }

    @RetryableTransaction
    public AccountPreferenceDto updateAccountPreference(UpdateAccountPreferenceCommand command, UUID id) {
        AccountPreference accountPreference = getExistingAccountPreference(id);

        Warehouse workWarehouse = getWarehouse(command.getWorkWarehouseId(), "Work warehouse not found.");
        Warehouse externalWarehouse = getWarehouse(command.getExternalWarehouseId(), "External warehouse not found.");

        accountPreference = accountPreference.update(command, workWarehouse, externalWarehouse);

        if (StringUtils.isNotBlank(command.getName())) {
            UpdateUserRequest request = new UpdateUserRequest();
            request.setName(command.getName());
            umsAdaptor.updateUser(accountPreference.getUserId(), request);
        }

        AccountPreferenceDto accountPreferenceDto = mapToDto(accountPreferenceRepository.save(accountPreference));
        dispatchUpdatedEvent(accountPreferenceDto);
        return accountPreferenceDto;
    }

    private void validateAccountPreferenceNotExists(String email) {
        AccountPreference existingPreference = accountPreferenceRepository.findByEmail(email);
        if (existingPreference != null) {
            throw new WmsBusinessException("Account preference already exists.");
        }
    }

    private Warehouse getWarehouse(UUID warehouseId, String errorMessage) {
        if (warehouseId == null) {
            return null;
        }
        return Optional.ofNullable(warehouseRepository.findById(warehouseId))
            .orElseThrow(() -> new WmsBusinessException(errorMessage));
    }

    private AccountPreference getExistingAccountPreference(UUID id) {
        return Optional.ofNullable(accountPreferenceRepository.findById(id))
            .orElseThrow(() -> new WmsBusinessException("Account preference not found."));
    }

    private AccountPreferenceDto mapToDto(AccountPreference accountPreference) {
        return accountPreferenceDtoApplicationMapper.domainToDto(accountPreference, null);
    }

    private void dispatchCreatedEvent(AccountPreferenceDto accountPreferenceDto) {
        businessEventDispatcher.dispatch(BusinessEventFactory.build(
            AccountPreferenceCreatedPayloadDto.builder()
                .data(accountPreferenceDto)
                .accountPreferenceId(accountPreferenceDto.getId())
                .build()));
    }

    private void dispatchUpdatedEvent(AccountPreferenceDto accountPreferenceDto) {
        businessEventDispatcher.dispatch(BusinessEventFactory.build(
            AccountPreferenceUpdatedPayloadDto.builder()
                .data(accountPreferenceDto)
                .accountPreferenceId(accountPreferenceDto.getId())
                .build()));
    }

}
