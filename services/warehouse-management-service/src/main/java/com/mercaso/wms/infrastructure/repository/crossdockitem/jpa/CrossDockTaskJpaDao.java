package com.mercaso.wms.infrastructure.repository.crossdockitem.jpa;

import com.mercaso.wms.infrastructure.repository.crossdock.jpa.dataobject.CrossDockTaskDo;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CrossDockTaskJpaDao extends JpaRepository<CrossDockTaskDo, UUID> {

    @Query("SELECT t FROM CrossDockTaskDo t WHERE t.deliveryDate = :deliveryDate AND t.pickerUserId = :pickerUserId")
    CrossDockTaskDo findByDeliveryDateAndPickerUserId(@Param("deliveryDate") String deliveryDate, @Param("pickerUserId") UUID pickerUserId);

}