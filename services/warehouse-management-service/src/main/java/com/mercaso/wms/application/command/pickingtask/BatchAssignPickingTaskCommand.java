package com.mercaso.wms.application.command.pickingtask;

import com.mercaso.wms.application.command.BaseCommand;
import java.util.List;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class BatchAssignPickingTaskCommand extends BaseCommand {

    private List<UUID> pickingTaskIds;

    private UUID pickerUserId;

    private String pickerUserName;

}
