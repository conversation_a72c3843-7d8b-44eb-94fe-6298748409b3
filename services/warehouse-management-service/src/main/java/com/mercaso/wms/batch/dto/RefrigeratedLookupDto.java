package com.mercaso.wms.batch.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefrigeratedLookupDto implements Serializable {

    @ExcelProperty(index = 0)
    private String itemNumber;
    @ExcelProperty(index = 2)
    private String itemDescription;
    @ExcelProperty(index = 6)
    private String department;
    @ExcelProperty(index = 7)
    private String category;
    @ExcelProperty(index = 8)
    private String subCategory;
    @ExcelProperty(index = 9)
    private String clazz;

}
