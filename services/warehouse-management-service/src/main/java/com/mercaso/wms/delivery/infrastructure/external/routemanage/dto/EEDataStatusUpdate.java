package com.mercaso.wms.delivery.infrastructure.external.routemanage.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class EEDataStatusUpdate {

    /**
     * Reported status-update time, in seconds since midnight (e.g.: update at 6pm = 64800)
     */
    @JsonProperty("sec")
    private Integer sec;
    /**
     * Flags the Order as either reschedule (to be rescheduled at some later date/time) or done (marked as done).
     * Sending null as value removes any previous status
     */
    @JsonProperty("status")
    private String status;
}
