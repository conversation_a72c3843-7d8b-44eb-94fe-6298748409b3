package com.mercaso.wms.application.query;

import com.mercaso.wms.domain.accountpreference.enums.AccountPreferenceStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.UUID;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class AccountPreferenceQuery {

    private String userName;

    private String email;

    private AccountPreferenceStatus status;

    private UUID workWarehouseId;

    private UUID externalWarehouseId;

    private PickingTaskType taskType;

}
