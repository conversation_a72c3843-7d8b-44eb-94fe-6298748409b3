package com.mercaso.user.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Locale;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.SerializationException;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SerializationUtils {


    private static final ObjectMapper objectMapper = standardObjectMapper();

    public static ObjectMapper standardObjectMapper() {
        ObjectMapper mapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        SimpleModule module = new SimpleModule();
        module.addDeserializer(LocalDateTime.class, new CustomLocalDateTimeDeserializer());
        mapper.registerModule(module);

        return mapper;
    }

    private static class CustomLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

        private static final DateTimeFormatter FORMATTER = new DateTimeFormatterBuilder()
            .parseCaseInsensitive()
            .appendPattern("MMM d yyyy h:mm:ss a")
            .toFormatter(Locale.ENGLISH);

        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String dateString = p.getText().trim();
            try {
                return LocalDateTime.parse(dateString, FORMATTER);
            } catch (Exception e) {
                throw new IOException("Error parsing date: " + dateString, e);
            }
        }
    }

    public static <T> T convertValue(Object content, TypeReference<T> valueTypeRef) {
        try {
            return objectMapper.convertValue(content, valueTypeRef);
        } catch (Exception e) {
            throw new SerializationException("readValue exception", e);
        }
    }
}
