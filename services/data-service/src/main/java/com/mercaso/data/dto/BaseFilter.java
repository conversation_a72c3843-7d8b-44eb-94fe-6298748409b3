package com.mercaso.data.dto;

import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;

@Data
public class BaseFilter {

    private CustomPageable pageable;

    
    
    /**
     * Get the default pageable with sorting
     * 
     * This is a default implementation using a fixed sorting rule (descending order by createdAt)
     * In the future, this method will be extended to allow users to pass in custom Sort
     * 
     * @return Pageable object containing pagination and sorting information
     * @see #getPageable(Sort) for the overloaded method that accepts custom sorting
     */
    public Pageable getPageable() {
        return getPageable(Sort.by(Direction.DESC, "createdAt"));
    }

    public Pageable getPageable(Sort sort) {
        return PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);
    }
}
