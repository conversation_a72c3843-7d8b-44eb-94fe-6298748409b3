package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogProductGenerationTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductGenerationTask;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationCompletedEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationCompletedPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogProductGenerationTaskMapper;
import com.mercaso.data.master_catalog.service.MasterCatalogGeneratePotentialDuplicationService;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class RemoveDuplicationCompletedEventListener implements
    ApplicationEventListener<RemoveDuplicationCompletedEvent, RemoveDuplicationCompletedPayload> {

    private final ApplicationEventPublisherProvider applicationEventPublisherProvider;
    private final MasterCatalogGeneratePotentialDuplicationService masterCatalogGeneratePotentialDuplicationService;
    private final MasterCatalogProductGenerationTaskMapper masterCatalogProductGenerationTaskMapper;

    @Override
    public void handleEvent(RemoveDuplicationCompletedEvent event) {
        log.info("REMOVE_DUPLICATION_COMPLETED listener start handle event.");
        try {
            RemoveDuplicationCompletedPayload payload = event.getPayload();
            MasterCatalogProductGenerationTaskDto taskDto = payload.getData();
            List<UUID> rawDataIds = payload.getRawDataIds();
            List<List<UUID>> duplicateRawIds = masterCatalogGeneratePotentialDuplicationService.generatePotentialDuplicationWithProduct(
                rawDataIds, "prod-product-table", 0.93F);
            MasterCatalogProductGenerationTask task = masterCatalogProductGenerationTaskMapper.toEntity(
                taskDto);
            applicationEventPublisherProvider.generateProductsInProgress(task,
                duplicateRawIds);

        } catch (Exception e) {
            log.error(
                "REMOVE_DUPLICATION_COMPLETED listener, error processing remove duplication event: {}",
                e.getMessage(), e);
            ;
        }
    }

}