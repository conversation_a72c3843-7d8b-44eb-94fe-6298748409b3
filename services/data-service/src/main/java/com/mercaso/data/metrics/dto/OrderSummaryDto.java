package com.mercaso.data.metrics.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderSummaryDto {
    private String id;
    private String name;
    private double amount;
    private String date;
    private List<OrderItemDto> items;
    private List<OrderDepDto> deps;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderItemDto {
        private String sku;
        private String name;
        private int count;
        private String depName;
        private double amount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderDepDto {
        private String depName;
        private int count;
        private double depAmount;
        private double depProportion;
    }
}