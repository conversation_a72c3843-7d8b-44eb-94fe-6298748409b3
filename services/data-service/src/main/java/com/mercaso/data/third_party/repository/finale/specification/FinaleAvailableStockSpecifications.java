package com.mercaso.data.third_party.repository.finale.specification;

import com.mercaso.data.third_party.entity.finale.FinaleAvailableStockEntity;
import com.mercaso.data.third_party.enums.finale.FinaleLocationTypeEnums;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FinaleAvailableStockSpecifications {

    private static final String STOCK_SUB_LOCALATIONS = "stockSublocations";

    public static Specification<FinaleAvailableStockEntity> byStockSubLocationsAndSkus(List<String> skus,
        FinaleLocationTypeEnums typeEnums) {
        return (Root<FinaleAvailableStockEntity> root, CriteriaQuery<?> query, CriteriaBuilder builder) -> {

            List<Predicate> predicates = new ArrayList<>();

            // Handling stockSublocations based on the enum value
            if (typeEnums != null && typeEnums.getValue() != null) {
                if (FinaleLocationTypeEnums.MFC.equals(typeEnums)) {
                    predicates.add(builder.notEqual(root.get(STOCK_SUB_LOCALATIONS), ""));
                    predicates.add(builder.notEqual(root.get(STOCK_SUB_LOCALATIONS), FinaleLocationTypeEnums.SHOPIFY.getValue()));
                } else {
                    predicates.add(builder.equal(root.get(STOCK_SUB_LOCALATIONS), typeEnums.getValue()));
                }
            }

            // Handling skus
            if (skus != null && !skus.isEmpty()) {
                predicates.add(root.get("sku").in(skus));
            }

            return builder.and(predicates.toArray(Predicate[]::new));
        };
    }
}