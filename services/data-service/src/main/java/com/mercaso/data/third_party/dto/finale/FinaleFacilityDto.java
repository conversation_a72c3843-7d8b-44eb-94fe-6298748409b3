package com.mercaso.data.third_party.dto.finale;

import java.time.LocalDateTime;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
public class FinaleFacilityDto {
    private String facilityId;
    private String facilityUrl;
    private String facilityTypeId;
    private String statusId;
    private LocalDateTime lastUpdatedDate;
    private LocalDateTime createdDate;
    private String actionUrlDeactivate;
    private String facilityName;
    private String parentFacilityUrl;
    private Boolean shippingDisabled;
    private String actionUrlActivate;
    private Boolean receivingDisabled;
    private Boolean returnReceivingDisabled;
    private String contactMechId;
    private String contactMechTypeId;
}