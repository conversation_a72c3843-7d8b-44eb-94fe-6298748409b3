package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogProductGenerationTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogProductGenerationTask;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.ProductGenerationTaskStatus;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationSubmitEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationSubmitPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogProductGenerationTaskMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogProductGenerationTaskRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RemoveDuplicationSubmitEventListener extends AbstractSubmitDuplicationEventListener
    implements ApplicationEventListener<RemoveDuplicationSubmitEvent, RemoveDuplicationSubmitPayload> {

    public RemoveDuplicationSubmitEventListener(
        MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository,
        MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository,
        MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService,
        MasterCatalogProductGenerationTaskRepository masterCatalogProductGenerationTaskRepository,
        MasterCatalogProductGenerationTaskMapper masterCatalogProductGenerationTaskMapper,
        ApplicationEventPublisherProvider applicationEventPublisherProvider,
        MasterCatalogRawDataRepository masterCatalogRawDataRepository) {
        super(masterCatalogPotentiallyDuplicateRawDataRepository, masterCatalogRawDataDuplicationRepository,
            masterCatalogRawDataDuplicationService, masterCatalogProductGenerationTaskRepository,
            masterCatalogProductGenerationTaskMapper, applicationEventPublisherProvider, masterCatalogRawDataRepository);
    }

    @Override
    public void handleEvent(RemoveDuplicationSubmitEvent event) {
        PotentiallyDuplicationContext context = createContext(event.getPayload());
        processRemoveDuplicationSubmit(context);
    }

    private void processRemoveDuplicationSubmit(PotentiallyDuplicationContext context) {
        if (anyMatchUnreviewedStatus(context.potentiallyDuplicateData())) {
            log.info("Task {} contains unreviewed data, skipping processing", context.taskId());
            return;
        }

        List<MasterCatalogPotentiallyDuplicateRawData> markedDuplicates =
            getMarkedDuplicates(context.potentiallyDuplicateData());

        if (!markedDuplicates.isEmpty()) {
            processDuplicationRecords(context.taskId(), markedDuplicates);
        }

        boolean allSubmitted = masterCatalogPotentiallyDuplicateRawDataRepository.findAllByTaskId(context.taskId())
            .stream().allMatch(data -> PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED == data.getStatus());

        if (allSubmitted) {
            updateTaskStatus(context);

            applicationEventPublisherProvider.publishEventRemoveDuplicationCompleted(context.task(),
                context.potentiallyDuplicateData());
        }
    }

    private void processDuplicationRecords(UUID taskId,
        List<MasterCatalogPotentiallyDuplicateRawData> markedDuplicates) {
        List<MasterCatalogRawDataDuplication> duplicationRecords = createDuplicationRecords(markedDuplicates);

        if (!duplicationRecords.isEmpty()) {
            masterCatalogRawDataDuplicationRepository.saveAll(duplicationRecords);
            log.info("Saved {} duplication records for task {}", duplicationRecords.size(), taskId);
        }
    }

    private PotentiallyDuplicationContext createContext(RemoveDuplicationSubmitPayload payload) {
        MasterCatalogProductGenerationTaskDto taskDto = payload.getData();
        MasterCatalogProductGenerationTask task = masterCatalogProductGenerationTaskMapper.toEntity(taskDto);
        List<MasterCatalogPotentiallyDuplicateRawData> duplicateData =
            masterCatalogPotentiallyDuplicateRawDataRepository.findAllByTaskId(taskDto.getId());

        return new PotentiallyDuplicationContext(taskDto.getId(), task, duplicateData);
    }

    @Override
    protected boolean anyMatchUnreviewedStatus(List<MasterCatalogPotentiallyDuplicateRawData> duplicateData) {
        return duplicateData.stream()
            .anyMatch(data -> data.getStatus() != PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
    }

    @Override
    protected void updateTaskStatus(PotentiallyDuplicationContext context) {
        MasterCatalogProductGenerationTask task = context.task();
        task.setStatus(ProductGenerationTaskStatus.REMOVE_DUPLICATION_COMPLETED);
        masterCatalogProductGenerationTaskRepository.save(task);
        log.info("Task {} remove duplication completed with status {}", task.getId(), task.getStatus());
    }
}
