package com.mercaso.data.metrics.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.List;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

@Entity
@Data
@Table(name = "metrics_territory_salesperson")
public class MetricsTerritorySalespersonEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "salesperson", nullable = false)
    private String salespersonName;

    @JdbcTypeCode(SqlTypes.ARRAY)
    @Column(name = "zip_code_list", columnDefinition = "text[]")
    private List<String> zipCodeList;
}
