buildscript {

    ext {
        springBootVersion = '3.3.0'
        lombokVersion = '1.18.30'
        javaVersion = JavaVersion.VERSION_21
        mapstructVersion = '1.6.0.Beta1'
        lombokMapstructBindingVersion = '0.2.0'
        postgresqlVersion = '42.6.0'
        kafkaVersion = '3.2.0'
        springCloudVersion = '2023.0.2'
        mercasoSecurityStarterVersion = '1.0.446'
        mercasoDocumentsOperationsVersion = '1.0.131'
        mercasoFeatureFlagStarterVersion = '1.0.400'
    }

    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}"
        classpath 'io.spring.gradle:dependency-management-plugin:1.0.11.RELEASE'
    }
}

plugins {
    id 'java'
    id 'io.spring.dependency-management' version '1.1.0'
    id 'org.flywaydb.flyway' version '10.14.0' apply false
    id 'maven-publish'
    id "org.sonarqube" version "4.4.1.3373" apply false
    id "jacoco"
    //id 'com.avast.gradle.docker-compose' version '0.17.6'
}

jacoco {
    toolVersion = "0.8.12"
}

def orgHostedRepository = findProperty('NEXUS_ORG_HOSTED_URL') ?: System.getenv('NEXUS_ORG_HOSTED_URL')
def nexusRepository = findProperty('NEXUS_URL') ?: System.getenv('NEXUS_URL')
def nexusUsername = findProperty('NEXUS_USERNAME') ?: System.getenv('NEXUS_USERNAME')
def nexusPassword = findProperty('NEXUS_PASSWORD') ?: System.getenv('NEXUS_PASSWORD')

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }
    repositories {
        maven {
            url = uri(orgHostedRepository)
            credentials {
                username = nexusUsername
                password = nexusPassword
            }
        }
    }
}

allprojects {
    repositories {
        maven {
            // Do not change this since it is used by the CI/CD pipeline as well
            url nexusRepository
            credentials {
                username = nexusUsername
                password = nexusPassword
            }
        }
    }

    group 'com.mercaso'
    version '1.0.0'

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
}

subprojects {
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'java'
//    apply plugin: 'groovy'
    apply plugin: 'org.flywaydb.flyway'

    dependencyManagement {
        imports {
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
            mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        }
        dependencies {
            dependency "org.projectlombok:lombok:${lombokVersion}"
        }
    }

    dependencies {
        implementation 'org.springframework.boot:spring-boot-starter'
        implementation 'org.springframework.boot:spring-boot-starter-web'
        implementation 'org.springframework.boot:spring-boot-starter-actuator'
        implementation "org.flywaydb:flyway-database-postgresql"
        implementation "org.postgresql:postgresql:${postgresqlVersion}"
        implementation "org.mapstruct:mapstruct:${mapstructVersion}", "org.projectlombok:lombok:${lombokVersion}"
        implementation 'org.springframework:spring-core:6.1.8'
        implementation "org.springframework.kafka:spring-kafka:${kafkaVersion}"
        implementation 'software.amazon.msk:aws-msk-iam-auth:2.2.0'
        implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'
        implementation 'org.springframework.boot:spring-boot-starter-data-jpa:3.3.0'
        implementation "io.opentelemetry:opentelemetry-sdk"
        implementation "io.opentelemetry:opentelemetry-exporter-otlp"
        implementation 'io.hypersistence:hypersistence-utils-hibernate-63:3.7.0'
        implementation 'org.springframework.cloud:spring-cloud-starter-vault-config'
        implementation 'org.springframework.cloud:spring-cloud-vault-config-databases'
        // security configuration
        implementation 'org.springframework.boot:spring-boot-starter-security'
        implementation "com.mercaso.components:mercaso-security-starter:${mercasoSecurityStarterVersion}"
        implementation "com.mercaso.components:mercaso-documents-operations:${mercasoDocumentsOperationsVersion}"
        implementation 'org.apache.skywalking:apm-toolkit-logback-1.x:9.2.0'
        implementation 'org.apache.skywalking:apm-toolkit-trace:9.0.0'
        implementation 'javax.servlet:javax.servlet-api:4.0.1'
        implementation 'com.alibaba:easyexcel:3.3.4'
        implementation 'org.springframework.statemachine:spring-statemachine-core:3.2.0'
        implementation "com.mercaso.components:mercaso-feature-flag-starter:${mercasoFeatureFlagStarterVersion}"
        implementation 'org.springframework.boot:spring-boot-starter-validation'


        annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}",
                "org.projectlombok:lombok:${lombokVersion}",
                "org.projectlombok:lombok-mapstruct-binding:${lombokMapstructBindingVersion}"
        testAnnotationProcessor "org.projectlombok:lombok"

        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.springframework.kafka:spring-kafka-test'
        testImplementation 'org.codehaus.groovy:groovy-all:3.0.9'
        testImplementation 'org.spockframework:spock-core:2.1-groovy-3.0'
    }

    test {
        description = 'Runs the unit tests'
        useJUnitPlatform()
        testLogging {
            events "PASSED", "SKIPPED", "FAILED", "STANDARD_OUT", "STANDARD_ERROR"
            exceptionFormat "full"
            showStandardStreams = true
        }
        include '**/*Test.class'
        exclude '**/*IT.class'
        finalizedBy jacocoTestReport
    }

    tasks.register('integrationTest', Test) {
        description = 'Runs the integration tests.'
        useJUnitPlatform()
        testLogging {
            events "PASSED", "SKIPPED", "FAILED", "STANDARD_OUT", "STANDARD_ERROR"
            exceptionFormat "full"
            showStandardStreams = true
        }
        include '**/*IT.class'
        exclude '**/*Test.class'
        finalizedBy jacocoTestReport
    }

    tasks.withType(Test).configureEach {
        maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
        forkEvery = 20
        reports.html.required = false
        reports.junitXml.required = true
    }

    check.dependsOn integrationTest
}
